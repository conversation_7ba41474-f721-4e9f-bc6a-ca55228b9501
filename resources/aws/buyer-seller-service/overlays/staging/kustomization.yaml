apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
 # High Priority Queue
  - target:
      group: sqs.aws.upbound.io
      kind: Queue
      name: listing-alerts-high-priority-
      version: v1beta1
    patch: |
      - op: replace
        path: /metadata/name
        value: listing-alerts-high-priority-staging
      - op: replace
        path: /spec/forProvider/name
        value: listing-alerts-high-priority-staging
      - op: replace
        path: /spec/forProvider/redrivePolicy
        value: |-
          {
            "deadLetterTargetArn": "arn:aws:sqs:us-east-1:093949242303:listing-alerts-high-priority-dlq-staging",
            "maxReceiveCount": 3
          }
  - target:
      group: sqs.aws.upbound.io
      kind: Queue
      name: listing-alerts-high-priority-dlq-
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: listing-alerts-high-priority-dlq-staging
      - op: replace
        path: /spec/forProvider/name
        value: listing-alerts-high-priority-dlq-staging

 # Medium Priority Queue
  - target:
      group: sqs.aws.upbound.io
      kind: Queue
      name: listing-alerts-medium-priority-
      version: v1beta1
    patch: |
      - op: replace
        path: /metadata/name
        value: listing-alerts-medium-priority-staging
      - op: replace
        path: /spec/forProvider/name
        value: listing-alerts-medium-priority-staging
      - op: replace
        path: /spec/forProvider/redrivePolicy
        value: |-
          {
            "deadLetterTargetArn": "arn:aws:sqs:us-east-1:093949242303:listing-alerts-medium-priority-dlq-staging",
            "maxReceiveCount": 3
          }
  - target:
      group: sqs.aws.upbound.io
      kind: Queue
      name: listing-alerts-medium-priority-dlq-
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: listing-alerts-medium-priority-dlq-staging
      - op: replace
        path: /spec/forProvider/name
        value: listing-alerts-medium-priority-dlq-staging

 # Low Priority Queue
  - target:
      group: sqs.aws.upbound.io
      kind: Queue
      name: listing-alerts-low-priority-
      version: v1beta1
    patch: |
      - op: replace
        path: /metadata/name
        value: listing-alerts-low-priority-staging
      - op: replace
        path: /spec/forProvider/name
        value: listing-alerts-low-priority-staging
      - op: replace
        path: /spec/forProvider/redrivePolicy
        value: |-
          {
            "deadLetterTargetArn": "arn:aws:sqs:us-east-1:093949242303:listing-alerts-low-priority-dlq-staging",
            "maxReceiveCount": 3
          }
  - target:
      group: sqs.aws.upbound.io
      kind: Queue
      name: listing-alerts-low-priority-dlq-
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: listing-alerts-low-priority-dlq-staging
      - op: replace
        path: /spec/forProvider/name
        value: listing-alerts-low-priority-dlq-staging

  # IAM
  - target:
      group: iam.aws.upbound.io
      kind: Role
      name: buyer-seller-service-role
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: buyer-seller-service-staging-role
  - target:
      group: iam.aws.upbound.io
      kind: RolePolicyAttachment
      name: buyer-seller-service-role-policy-attachment
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: buyer-seller-service-role-policy-attachment-staging
  - target:
      group: iam.aws.upbound.io
      kind: Policy
      name: buyer-seller-service-policy
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: buyer-seller-service-staging-policy
  - path: iam-role-installer.yaml
  - path: iam-role-policy-attachment-installer.yaml
