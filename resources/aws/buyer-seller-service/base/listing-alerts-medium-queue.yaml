apiVersion: sqs.aws.upbound.io/v1beta1
kind: Queue
metadata:
  name: listing-alerts-medium-priority-
spec:
  forProvider:
    name: listing-alerts-medium-priority-
    region: us-east-1
    redrivePolicy: |-
      {
        "deadLetterTargetArn": "arn:aws:sqs:us-east-1:to-be-replaced:listing-alerts-medium-priority-dlq-",
        "maxReceiveCount": 3
      }
  providerConfigRef:
    name: provider-config-aws
---
apiVersion: sqs.aws.upbound.io/v1beta1
kind: Queue
metadata:
  name: listing-alerts-medium-priority-dlq-
spec:
  forProvider:
    region: us-east-1
    name: listing-alerts-medium-priority-dlq-
  providerConfigRef:
    name: provider-config-aws
