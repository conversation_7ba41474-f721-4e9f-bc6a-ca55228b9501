apiVersion: iam.aws.upbound.io/v1beta1
kind: Role
metadata:
  name: buyer-seller-service-role
spec:
  forProvider:
    assumeRolePolicy: |-
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "Federated": "federated-to-set"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
              "StringEquals": {
                "condition-string": "condition-value"
              }
            }
          }
        ]
      }
  providerConfigRef:
    name: provider-config-aws
