apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: buyer-seller-service-policy
spec:
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Action": [
                    "sqs:ListQueues",
                    "sqs:GetQueueAttributes",
                    "sqs:GetQueueUrl"
                  ],
                  "Resource": "*"
              },
              {
                "Effect": "Allow",
                "Action": [
                  "sqs:Get*",
                  "sqs:List*",
                  "sqs:SendMessage",
                  "sqs:ReceiveMessage",
                  "sqs:DeleteMessage",
                  "sqs:DeleteMessageBatch",
                  "sqs:PurgeQueue"
                ],
                "Resource": [
                  "arn:aws:sqs:us-east-1:*:listing-alerts-*"
                ]
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
