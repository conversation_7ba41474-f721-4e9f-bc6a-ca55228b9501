apiVersion: sqs.aws.upbound.io/v1beta1
kind: Queue
metadata:
  name: listing-alerts-high-priority-
spec:
  forProvider:
    name: listing-alerts-high-priority-
    region: us-east-1
    redrivePolicy: |-
      {
        "deadLetterTargetArn": "arn:aws:sqs:us-east-1:to-be-replaced:listing-alerts-high-priority-dlq-",
        "maxReceiveCount": 3
      }
  providerConfigRef:
    name: provider-config-aws
---
apiVersion: sqs.aws.upbound.io/v1beta1
kind: Queue
metadata:
  name: listing-alerts-high-priority-dlq-
spec:
  forProvider:
    region: us-east-1
    name: listing-alerts-high-priority-dlq-
  providerConfigRef:
    name: provider-config-aws
