apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: api-gateway-policy
spec:
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Action": "s3:*",
                  "Resource": [
                      "arn:aws:s3:::lp-etl-property-media-load*",
                      "arn:aws:s3:::luxurycoders-user-uploads*",
                      "arn:aws:s3:::lp-copilot-resources*"
                  ]
              },
              {
                  "Effect": "Allow",
                  "Action": [
                      "sns:ListTopics",
                      "sns:Unsubscribe",
                      "sns:CreatePlatformEndpoint",
                      "sns:OptInPhoneNumber",
                      "sns:CheckIfPhoneNumberIsOptedOut",
                      "sns:ListEndpointsByPlatformApplication",
                      "sns:SetEndpointAttributes",
                      "sns:DeletePlatformApplication",
                      "sns:SetPlatformApplicationAttributes",
                      "sns:VerifySMSSandboxPhoneNumber",
                      "sns:DeleteSMSSandboxPhoneNumber",
                      "sns:ListSMSSandboxPhoneNumbers",
                      "sns:CreatePlatformApplication",
                      "sns:SetSMSAttributes",
                      "sns:GetPlatformApplicationAttributes",
                      "sns:GetSubscriptionAttributes",
                      "sns:ListSubscriptions",
                      "sns:ListOriginationNumbers",
                      "sns:DeleteEndpoint",
                      "sns:ListPhoneNumbersOptedOut",
                      "sns:GetEndpointAttributes",
                      "sns:SetSubscriptionAttributes",
                      "sns:GetSMSSandboxAccountStatus",
                      "sns:CreateSMSSandboxPhoneNumber",
                      "sns:ListPlatformApplications",
                      "sns:GetSMSAttributes"
                  ],
                  "Resource": "*"
              },
              {
                  "Effect": "Allow",
                  "Action": "sns:*",
                  "Resource": "arn:aws:sns:*:*:integrations*"
              },
              {
                  "Effect": "Allow",
                  "Action": [
                      "sqs:SendMessage"
                  ],
                  "Resource": "arn:aws:sqs:*:*:webhook-sqs-*"
              },
              {
                  "Effect": "Allow",
                  "Action": [
                      "sqs:ListQueues",
                      "sqs:GetQueueUrl",
                      "sqs:ListDeadLetterSourceQueues",
                      "sqs:GetQueueAttributes",
                      "sqs:ListQueueTags",
                      "secretsmanager:BatchGetSecretValue",
                      "secretsmanager:ListSecrets",
                      "secretsmanager:DescribeSecret",
                      "secretsmanager:GetSecretValue"
                  ],
                  "Resource": "*"
              },
              {
                  "Effect": "Allow",
                  "Action": "sqs:*",
                  "Resource": [
                      "arn:aws:sqs:us-east-1:*:dedupe-*",
                      "arn:aws:sqs:us-east-1:*:dedupe-*.fifo"
                  ]
              },
              {
                  "Effect": "Allow",
                  "Action": [
                    "s3:List*",
                    "s3:Get*",
                    "s3:PutObject",
                    "s3:DeleteObject",
                    "s3:DeleteObjectVersion"
                  ],
                  "Resource": [
                    "arn:aws:s3:::lp-mmc-leads-uploads-*",
                    "arn:aws:s3:::lp-mmc-leads-uploads-*/*"
                  ]
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
