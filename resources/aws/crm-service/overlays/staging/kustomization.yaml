apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - target:
      group: iam.aws.upbound.io
      kind: Role
      name: crm-service-role
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: crm-service-staging-role
  - target:
      group: iam.aws.upbound.io
      kind: Policy
      name: crm-service-policy
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: crm-service-staging-policy
  - target:
      group: iam.aws.upbound.io
      kind: RolePolicyAttachment
      name: crm-service-role-policy-attachment
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: crm-service-staging-role-policy-attachment
  - target:
      group: s3.aws.upbound.io
      kind: Bucket
      name: lp-mmc-leads-uploads-
      version: v1beta1
    patch: |
      - op: replace
        path: /metadata/name
        value: lp-mmc-leads-uploads-staging
  - path: iam-role.yaml
  - path: iam-policy.yaml
  - path: iam-role-policy-attachment.yaml
