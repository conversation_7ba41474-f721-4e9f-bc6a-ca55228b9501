apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: crm-service-policy
spec:
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Action": "sqs:ListQueues",
                  "Resource": "*"
              },
              {
                  "Effect": "Allow",
                  "Action": "sns:*",
                  "Resource": "arn:aws:sns:*:*:integrations*"
              },
              {
                  "Effect": "Allow",
                  "Action": [
                    "s3:List*",
                    "s3:Get*",
                    "s3:PutObject",
                    "s3:DeleteObject",
                    "s3:DeleteObjectVersion"
                  ],
                  "Resource": [
                    "arn:aws:s3:::lp-mmc-leads-uploads-*",
                    "arn:aws:s3:::lp-mmc-leads-uploads-*/*"
                  ]
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
