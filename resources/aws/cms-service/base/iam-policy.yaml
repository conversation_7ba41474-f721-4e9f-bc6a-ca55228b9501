apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: cms-service-policy
spec:
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Action": [
                    "sqs:ListQueues",
                    "s3:List*"
                  ],
                  "Resource": "*"
              },
              {
                  "Effect": "Allow",
                  "Action": "sqs:*",
                  "Resource": [
                    "arn:aws:sqs:us-east-1:*:network-company*",
                    "arn:aws:sqs:us-east-1:*:dedupe-render*"
                  ]
              },
              {
                  "Effect": "Allow",
                  "Action": [
                    "s3:List*",
                    "s3:Get*",
                    "s3:PutObject",
                    "s3:DeleteObject",
                    "s3:DeleteObjectVersion"
                  ],
                  "Resource": [
                    "arn:aws:s3:::cmt-web-scraper-*",
                    "arn:aws:s3:::cmt-web-scraper-*/*",
                    "arn:aws:s3:::luxurycoders-user-uploads",
                    "arn:aws:s3:::luxurycoders-user-uploads/*"
                  ]
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
