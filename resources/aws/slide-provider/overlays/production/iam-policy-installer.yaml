apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: slide-provider-production-policy
  annotations:
    crossplane.io/external-name: slide-provider-production-policy
spec:
  deletionPolicy: <PERSON>pha<PERSON>
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
              {
                  "Effect": "Allow",
                  "Action": [
                      "s3:List*",
                      "s3:GetObjectVersionTagging",
                      "s3:GetStorageLensConfigurationTagging",
                      "s3:GetObjectAcl",
                      "s3:GetBucketObjectLockConfiguration",
                      "s3:GetIntelligentTieringConfiguration",
                      "s3:GetObjectVersionAcl",
                      "s3:GetBucketPolicyStatus",
                      "s3:GetObjectRetention",
                      "s3:GetBucketWebsite",
                      "s3:GetJobTagging",
                      "s3:GetMultiRegionAccessPoint",
                      "s3:GetObjectAttributes",
                      "s3:GetObjectLegalHold",
                      "s3:GetBucketNotification",
                      "s3:DescribeMultiRegionAccessPointOperation",
                      "s3:GetReplicationConfiguration",
                      "s3:GetObject",
                      "s3:DescribeJob",
                      "s3:GetAnalyticsConfiguration",
                      "s3:GetObjectVersionForReplication",
                      "s3:GetAccessPointForObjectLambda",
                      "s3:GetStorageLensDashboard",
                      "s3:GetLifecycleConfiguration",
                      "s3:GetInventoryConfiguration",
                      "s3:GetBucketTagging",
                      "s3:GetAccessPointPolicyForObjectLambda",
                      "s3:GetBucketLogging",
                      "s3:GetAccelerateConfiguration",
                      "s3:GetObjectVersionAttributes",
                      "s3:GetBucketPolicy",
                      "s3:GetEncryptionConfiguration",
                      "s3:GetObjectVersionTorrent",
                      "s3:GetBucketRequestPayment",
                      "s3:GetAccessPointPolicyStatus",
                      "s3:GetObjectTagging",
                      "s3:GetMetricsConfiguration",
                      "s3:GetBucketOwnershipControls",
                      "s3:GetBucketPublicAccessBlock",
                      "s3:GetMultiRegionAccessPointPolicyStatus",
                      "s3:GetMultiRegionAccessPointPolicy",
                      "s3:GetAccessPointPolicyStatusForObjectLambda",
                      "s3:GetBucketVersioning",
                      "s3:GetBucketAcl",
                      "s3:GetAccessPointConfigurationForObjectLambda",
                      "s3:GetObjectTorrent",
                      "s3:GetStorageLensConfiguration",
                      "s3:GetBucketCORS",
                      "s3:GetBucketLocation",
                      "s3:GetAccessPointPolicy",
                      "s3:GetObjectVersion"
                  ],
                  "Resource": [
                      "arn:aws:s3:::lp-presentations",
                      "arn:aws:s3:::lp-presentations/*"
                  ]
              }
          ]
      }
  providerConfigRef:
    name: provider-config-aws
