apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name:  mls-data-ai-mwaa-mcp-policy
spec:
  forProvider:
    policy: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Action": [
              "airflow:CreateCliToken",
              "airflow:CreateWebLoginToken",
              "airflow:GetEnvironment",
              "airflow:ListEnvironments",
               "airflow:InvokeRestApi"
            ],
            "Resource": "*"
          }
        ]
      }
  providerConfigRef:
    name: provider-config-aws
