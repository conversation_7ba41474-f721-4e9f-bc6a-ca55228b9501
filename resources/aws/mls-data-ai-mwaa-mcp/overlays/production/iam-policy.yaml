apiVersion: iam.aws.upbound.io/v1beta1
kind: Policy
metadata:
  name: mls-data-ai-mwaa-mcp-policy
spec:
  deletionPolicy: Orphan
  forProvider:
    policy: |
      {
          "Version": "2012-10-17",
          "Statement": [
                {
                  "Effect": "Allow",
                  "Action": [
                    "airflow:CreateCliToken",
                    "airflow:CreateWebLoginToken",
                    "airflow:GetEnvironment",
                    "airflow:ListEnvironments",
                     "airflow:InvokeRestApi"
                  ],
                  "Resource": "arn:aws:airflow:us-east-1:381475384502:environment/data-etl-mmaa-production-etl"
                }
          ]
      }
