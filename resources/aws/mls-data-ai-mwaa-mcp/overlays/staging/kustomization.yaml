apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - target:
      group: iam.aws.upbound.io
      kind: Role
      name: mls-data-ai-mwaa-mcp-role
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: mls-data-ai-mwaa-mcp-staging-role
  - target:
      group: iam.aws.upbound.io
      kind: Policy
      name: mls-data-ai-mwaa-mcp-policy
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: mls-data-ai-mwaa-mcp-staging-policy
  - target:
      group: iam.aws.upbound.io
      kind: RolePolicyAttachment
      name: mls-data-ai-mwaa-mcp-role-policy-attachment
      version: v1beta1
    patch: |-
      - op: replace
        path: /metadata/name
        value: mls-data-ai-mwaa-mcp-staging-role-policy-attachment
  - path: iam-role.yaml
  - path: iam-policy.yaml
  - path: iam-role-policy-attachment.yaml
