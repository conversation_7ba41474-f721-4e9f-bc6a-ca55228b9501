apiVersion: iam.aws.upbound.io/v1beta1
kind: Role
metadata:
  name: mls-data-ai-mwaa-mcp-role
spec:
  forProvider:
    assumeRolePolicy: |-
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Allow",
            "Principal": {
              "Federated": "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/0B0E16CE9DBD3BAA2DD34C6C2CA3543E"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
              "StringEquals": {
                "oidc.eks.us-east-1.amazonaws.com/id/0B0E16CE9DBD3BAA2DD34C6C2CA3543E:sub": "system:serviceaccount:apps:mls-data-ai-mwaa-mcp-sa"
              }
            }
          }
        ]
      }
