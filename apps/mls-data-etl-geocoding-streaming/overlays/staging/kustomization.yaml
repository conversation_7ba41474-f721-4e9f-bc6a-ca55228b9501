apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - mls-data-etl-geocoding-streaming-dd-metric.yaml
patches:
  - path: deploy.yaml
  - path: dlq-deploy.yaml
  - path: sa.yaml
  - path: hpa.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-etl-geocoding-streaming
    newTag: main-14673-45d94ff # {"$imagepolicy": "flux-system:mls-data-etl-geocoding-streaming-flux-image-policy-staging-new:tag"}
