apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-ssr-service-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v0.7.26 # {"lp-deploy-tag-updater:version": "website-ssr-service"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v0.7.26 # {"lp-deploy-tag-updater:version": "website-ssr-service"}
    spec:
      containers:
        - name: website-ssr-service
          resources:
            limits:
              cpu: "500m"
              memory: "350Mi"
            requests:
              cpu: "100m"
              memory: "200Mi"
