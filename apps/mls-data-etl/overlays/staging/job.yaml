apiVersion: batch/v1
kind: Job
metadata:
  name: mls-data-etl-dags-job
spec:
  template:
    spec:
      containers:
        - name: aws-cli
          env:
            - name: DAGS_BUCKETS
              value: "lp-data-mwaa-staging1"
            - name: ENV
              value: "staging"
            - name: VERSION
              value: main-14694-364873f # {"$imagepolicy": "flux-system:mls-data-etl-flux-image-policy-staging-new:tag"}
