apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - sa-dags-pods.yaml
  - external_secrets.yaml
transformers:
  - version-suffixer.yaml
images:
  - name: luxurypresence/data-etl-dags
    newTag: v0.5.2 # {"$imagepolicy": "flux-system:mls-data-etl-flux-image-policy-production:tag"}
patches:
  - path: job.yaml
  - path: sa.yaml
  - path: cronjob.yaml
  - target:
      group: batch
      kind: Job
      name: mls-data-etl-dags-job
      version: v1
    patch: |-
      - op: replace
        path: /metadata/name
        value: mls-data-etl-dags-job-production-
  - target:
      group: batch
      kind: CronJob
      name: mwaa-pod-terminator
      version: v1
    patch: |-
      - op: replace
        path: /metadata/namespace
        value: mwaa-production
  - target:
      group: rbac.authorization.k8s.io
      version: v1
    patch: |-
      - op: replace
        path: /metadata/namespace
        value: mwaa-production
  - target:
      kind: ServiceAccount
      name: mwaa-pod-terminator
      version: v1
    patch: |-
      - op: replace
        path: /metadata/namespace
        value: mwaa-production
