apiVersion: batch/v1
kind: Job
metadata:
  name: mls-data-etl-dags-job
spec:
  template:
    spec:
      containers:
        - name: aws-cli
          env:
            - name: ENV
              value: "production"
            - name: VERSION
              value: v0.5.2 # {"$imagepolicy": "flux-system:mls-data-etl-flux-image-policy-production:tag"}
          command:
            - /bin/sh
            - -e
            - -c
            - |
              aws s3 sync /dags/ s3://lp-data-mwaa-production-etl/dags --exclude "partial_sync_*" --exclude "partial_sync_streaming*" --exclude "cdc_and_notify_property_streaming_sync*"
              aws s3 sync /dags/ s3://lp-data-mwaa-production-streaming-syncs/dags --exclude "*" --include "partial_sync_streaming*" --include "plugins/*" --include "utils/*" --include "configs/*" --include ".airflowignore" --include "kube_config.yaml" --include "airflow_*.txt" --include "airflow_metadata_extract/*" --include "cdc_and_notify_property_streaming_sync*"
              sed -e "s/<ENV>/$ENV/g" -e "s/<ARTIFACT>/$ARTIFACT/g" -e "s/<VERSION>/$VERSION/g" -e "s/<BUCKET>/lp-data-mwaa-production-\*/g" /slack/template.json > ./data.json
              curl -X POST -H "Content-type: application/json; charset=utf-8" -d @./data.json $SLACK_WEBHOOK_URL
          resources:
            limits:
              cpu: "2"
              memory: 1Gi
            requests:
              cpu: "1"
              memory: 1Gi
          volumeMounts:
            - mountPath: /dags
              name: dags
