apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-deployment
  labels:
    app: MlsDataAiMwaaMcp
    tags.datadoghq.com/env: custom
    tags.datadoghq.com/service: mls-data-ai-mwaa-mcp
    tags.datadoghq.com/version: latest
spec:
  selector:
    matchLabels:
      run: mls-data-ai-mwaa-mcp
  template:
    metadata:
      annotations:
        ad.datadoghq.com/tags: '{"team": "data"}'
        karpenter.sh/do-not-disrupt: "true"
      labels:
        app: MlsDataAiMwaaMcp
        run: mls-data-ai-mwaa-mcp
        tags.datadoghq.com/env: custom
        tags.datadoghq.com/service: mls-data-ai-mwaa-mcp
        tags.datadoghq.com/version: latest
        luxurypresence/repository-name: mls-data-etl
        luxurypresence/package-name: mls-data-ai-mwaa-mcp
    spec:
      serviceAccountName: mls-data-ai-mwaa-mcp-sa
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "nodepool-role"
                operator: In
                values:
                - "mwaa-deployments"
      tolerations:
      - key: node-group-role
        operator: Equal
        value: "mwaa-deployments"
        effect: NoSchedule
      initContainers:
        - name: grab-config
          env:
            - name: CONFIG_TEMPLATE_PATH
              value: etl/extract/configs/config-template.yaml
      containers:
        - name: app
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "1500m"
              memory: "4Gi"
            requests:
              cpu: "1500m"
              memory: "4Gi"
          ports:
            - containerPort: 8022
          # the readiness probe details
          readinessProbe:
            httpGet:
              port: 8022
              path: /ping
              scheme: HTTP # or HTTPS
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            failureThreshold: 3
            timeoutSeconds: 15
          # the livenessProbe probe details
          livenessProbe:
            httpGet:
              port: 8022
              path: /health
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 120
            successThreshold: 1
            failureThreshold: 5
            timeoutSeconds: 15
          volumeMounts:
            - mountPath: /usr/src/app/etl/extract/configs/config.yaml
              name: app-config
              subPath: config.yaml
