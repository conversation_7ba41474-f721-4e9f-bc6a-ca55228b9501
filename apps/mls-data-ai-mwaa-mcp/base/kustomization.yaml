apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../_baseline/microservice/v1
- sa.yml
- service.yml
- hpa.yaml
- ingress-internal.yaml
- ingress-public.yaml
patches:
- path: deploy.yml
- patch: |-
    - op: replace
      path: /metadata/name
      value: mls-data-ai-mwaa-mcp-deployment
    - op: replace
      path: /spec/template/spec/containers/0/name
      value: mls-data-ai-mwaa-mcp
  target:
    group: apps
    kind: Deployment
    name: app-deployment
    version: v1
