apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/group.name: global-internal-ingress
  name: mls-data-ai-mwaa-mcp-ingress-internal
spec:
  rules:
  - host: mls-data-ai-mwaa-mcp.luxurycoders.com
    http:
      paths:
      - backend:
          service:
            name: ssl-redirect
            port:
              name: use-annotation
        path: /*
        pathType: ImplementationSpecific
      - backend:
          service:
            name: mls-data-ai-mwaa-mcp
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific
