apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - path: deploy.yaml
  - path: sa.yaml
  - path: hpa.yaml
  - path: ingress-internal.yaml
  - path: ingress-public.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-ai-mwaa-mcp
    newTag: main-13528-e709a76 # {"$imagepolicy": "flux-system:mls-data-ai-mwaa-mcp-flux-image-policy-staging-new:tag"}
