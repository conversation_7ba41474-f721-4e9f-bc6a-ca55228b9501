apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/group.name: global-ingress
  name: mls-data-ai-mwaa-mcp-ingress-public
spec:
  rules:
  - host: mls-data-ai-mwaa-mcp-public.luxurycoders.com
    http:
      paths:
      - backend:
          service:
            name: mls-data-ai-mwaa-mcp
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific
