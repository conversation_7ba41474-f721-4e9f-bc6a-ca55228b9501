apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-ai-mwaa-mcp-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: main-13528-e709a76 # {"$imagepolicy": "flux-system:mls-data-ai-mwaa-mcp-flux-image-policy-staging-new:tag"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-ai-mwaa-mcp-sa
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: main-13528-e709a76 # {"$imagepolicy": "flux-system:mls-data-ai-mwaa-mcp-flux-image-policy-staging-new:tag"}
