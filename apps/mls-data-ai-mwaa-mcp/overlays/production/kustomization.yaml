apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - path: deploy.yaml
  - path: hpa.yaml
  - path: sa.yaml
  - path: ingress-internal.yaml
  - path: ingress-public.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-ai-mwaa-mcp
    newTag: v0.4.37 # {"$imagepolicy": "flux-system:mls-data-ai-mwaa-mcp-flux-image-policy-production:tag"}
