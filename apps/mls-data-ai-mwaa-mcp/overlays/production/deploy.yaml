apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-ai-mwaa-mcp-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.4.37 # {"$imagepolicy": "flux-system:mls-data-ai-mwaa-mcp-flux-image-policy-production:tag"}
spec:
  replicas: 0
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-ai-mwaa-mcp-sa-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.4.37 # {"$imagepolicy": "flux-system:mls-data-ai-mwaa-mcp-flux-image-policy-production:tag"}
