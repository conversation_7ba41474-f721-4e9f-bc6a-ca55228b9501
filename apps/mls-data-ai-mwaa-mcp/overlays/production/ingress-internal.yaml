apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:381475384502:certificate/6bdc60fc-dd5b-447c-a75e-d719e32d361f
    alb.ingress.kubernetes.io/group.name: global-internal-ingress
  name: mls-data-ai-mwaa-mcp-ingress-internal
spec:
  rules:
  - host: mls-data-ai-mwaa-mcp.luxurypresence.com
    http:
      paths:
      - backend:
          service:
            name: ssl-redirect
            port:
              name: use-annotation
        path: /*
        pathType: ImplementationSpecific
      - backend:
          service:
            name: mls-data-ai-mwaa-mcp
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific
