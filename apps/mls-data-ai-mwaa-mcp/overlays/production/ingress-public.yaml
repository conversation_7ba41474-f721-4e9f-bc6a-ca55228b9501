apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mls-data-ai-mwaa-mcp-ingress-public
  annotations:
    alb.ingress.kubernetes.io/group.name: global-ingress
spec:
  rules:
  - host: mls-data-ai-mwaa-mcp-public.luxurypresence.com
    http:
      paths:
      - backend:
          service:
            name: mls-data-ai-mwaa-mcp
            port:
              number: 80
        path: /api/v1/webhook/posthog/events
        pathType: Prefix
