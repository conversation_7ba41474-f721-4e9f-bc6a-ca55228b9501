apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-provider-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v0.15.0 # {"lp-deploy-tag-updater:version": "website-provider"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v0.15.0 # {"lp-deploy-tag-updater:version": "website-provider"}
    spec:
      containers:
        - name: website-provider
          resources:
            limits:
              cpu: "500m"
              memory: "350Mi"
            requests:
              cpu: "100m"
              memory: "200Mi"
