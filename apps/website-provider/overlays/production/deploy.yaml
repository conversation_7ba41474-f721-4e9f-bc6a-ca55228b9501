apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-provider-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.15.0 # {"lp-deploy-tag-updater:version": "website-provider"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: website-provider-sa-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.15.0 # {"lp-deploy-tag-updater:version": "website-provider"}
    spec:
      containers:
        - name: website-provider
          resources:
            limits:
              cpu: "1.2"
              memory: "1Gi"
            requests:
              cpu: "0.7"
              memory: "1Gi"
