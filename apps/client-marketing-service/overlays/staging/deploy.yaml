apiVersion: apps/v1
kind: Deployment
metadata:
  name: client-marketing-service-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v10.8.1 # {"lp-deploy-tag-updater:version": "client-marketing-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: client-marketing-service-sa
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v10.8.1 # {"lp-deploy-tag-updater:version": "client-marketing-service"}
    spec:
      containers:
        - name: client-marketing-service
          env:
            - name: PG_CONNECTION_STRING
              value: "postgresql://${vault:database/creds/postgres-staging-crew-ai-seo-automation-long-life-v4#username}:${vault:database/creds/postgres-staging-crew-ai-seo-automation-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}?schema=crew_ai_seo_automation"
              valueFrom:
                $patch: delete
            - name: PG_READER_CONNECTION_STRING
              value: "postgresql://${vault:database/creds/postgres-staging-crew-ai-seo-automation-long-life-v4#username}:${vault:database/creds/postgres-staging-crew-ai-seo-automation-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#READER_HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}?schema=crew_ai_seo_automation"
