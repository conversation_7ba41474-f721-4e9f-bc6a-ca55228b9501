apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base

patches:
  - path: deploy.yaml
  - path: load-sql-migration-job.yaml
  - path: ingress-internal.yaml
  - path: graphql-publish.yaml
  - patch: |-
      - op: replace
        path: /metadata/name
        value: client-marketing-service-graphql-publish-
    target:
      kind: Job
      name: app-cronjob-graphql
      version: v1
      group: batch
images:
  - name: luxurypresence/load-client-marketing-service-migration
    newName: luxurypresence/load-client-marketing-service-migration
    newTag: v10.8.1 # {"lp-deploy-tag-updater:version": "client-marketing-service"}
  - name: luxurypresence/app
    newName: luxurypresence/client-marketing-service
    newTag: v10.8.1 # {"lp-deploy-tag-updater:version": "client-marketing-service"}

transformers:
  - version-suffixer.yaml
