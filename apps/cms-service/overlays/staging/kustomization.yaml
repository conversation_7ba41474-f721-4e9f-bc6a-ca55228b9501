apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - ingress-airops.yaml
patches:
  - path: load-sql-migration-job.yaml
  - path: graphql-publish.yaml
  - path: deploy.yaml
  - path: ingress.yaml
  - path: sa.yaml
  - patch: |-
      - op: replace
        path: /metadata/name
        value: cms-service-graphql-publish-
    target:
      kind: Job
      name: app-cronjob-graphql
      version: v1
      group: batch
images:
  - name: luxurypresence/load-cms-migration
    newName: luxurypresence/load-cms-migration
    newTag: v4.4.0 # {"lp-deploy-tag-updater:version": "cms-service"}
  - name: luxurypresence/app
    newName: luxurypresence/cms-service
    newTag: v4.4.0 # {"lp-deploy-tag-updater:version": "cms-service"}

transformers:
  - version-suffixer.yaml