apiVersion: apps/v1
kind: Deployment
metadata:
  name: cms-service-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v4.4.0 # {"lp-deploy-tag-updater:version": "cms-service"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v4.4.0 # {"lp-deploy-tag-updater:version": "cms-service"}
    spec:
      initContainers:
        - name: grab-config
        - name: render-config
          env:
            - name: DATABASE_NAME
              value: vault:secret/data/staging/database-info/main-v4#DB
              valueFrom:
                $patch: delete
      containers:
        - name: cms-service
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-property-long-life-v4#username}:${vault:database/creds/postgres-staging-property-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}'
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-property-long-life-v4#username}:${vault:database/creds/postgres-staging-property-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#READER_HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}'
            - name: VAULT_LOG_LEVEL
              value: ERROR
          resources:
            limits:
              cpu: "800m"
              memory: "450Mi"
            requests:
              cpu: "400m"
              memory: "300Mi"
