apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-etl-media-streaming-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: main-14673-45d94ff # {"$imagepolicy": "flux-system:mls-data-etl-media-streaming-flux-image-policy-staging-new:tag"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-etl-media-streaming-sa
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: main-14673-45d94ff # {"$imagepolicy": "flux-system:mls-data-etl-media-streaming-flux-image-policy-staging-new:tag"}
