apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - ingress-airops.yaml
patches:
  - path: deploy.yaml
  - path: deploy-consumer.yml
  - path: sa.yaml
  - path: load-sql-migration-job.yaml
  - path: ingress.yaml
  - path: graphql-publish.yaml
  - patch: |-
      - op: replace
        path: /metadata/name
        value: crm-service-graphql-publish-
    target:
      kind: Job
      name: app-cronjob-graphql
      version: v1
      group: batch

images:
  - name: luxurypresence/load-crm-migration
    newName: luxurypresence/load-crm-migration
    newTag: v2.4.0 # {"lp-deploy-tag-updater:version": "crm-service"}
  - name: luxurypresence/app
    newName: luxurypresence/crm-service
    newTag: v2.4.0 # {"lp-deploy-tag-updater:version": "crm-service"}
  - name: luxurypresence/app
    newName: luxurypresence/crm-consumer
    newTag: v2.4.0 # {"lp-deploy-tag-updater:version": "crm-service"}

transformers:
  - version-suffixer.yaml
