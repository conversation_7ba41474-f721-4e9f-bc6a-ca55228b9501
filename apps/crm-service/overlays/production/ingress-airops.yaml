apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/whitelist-source-range: ***********,***************
    nginx.ingress.kubernetes.io/configuration-snippet: |
      if ($apikey_is_ok != crm-key) {
        return 401;
      }
  name: crm-service-airops-ingress
spec:
  ingressClassName: nginx
  rules:
  - host: crm.luxurypresence.com
    http:
      paths:
      - backend:
          service:
            name: crm-service
            port:
              number: 80
        path: /
        pathType: Prefix
