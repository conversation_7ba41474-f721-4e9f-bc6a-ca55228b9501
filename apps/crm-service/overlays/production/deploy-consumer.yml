apiVersion: apps/v1
kind: Deployment
metadata:
  name: crm-consumer-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v2.4.0 # {"lp-deploy-tag-updater:version": "crm-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: crm-service-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v2.4.0 # {"lp-deploy-tag-updater:version": "crm-service"}
    spec:
      containers:
        - name: crm-consumer
          env:
            - name: JOB_TYPE
              value: "CONSUMER"
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-production-crm-long-life-v3#username}:${vault:database/creds/postgres-production-crm-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}'
              valueFrom:
                $patch: delete
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-production-crm-long-life-v3#username}:${vault:database/creds/postgres-production-crm-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#READER_HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}'
          resources:
            limits:
              cpu: "1"
              memory: "615Mi"
            requests:
              cpu: "500m"
              memory: "615Mi"
