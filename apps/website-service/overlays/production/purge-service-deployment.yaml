apiVersion: apps/v1
kind: Deployment
metadata:
  name: purge-service-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v1.39.8 # {"lp-deploy-tag-updater:version": "website-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: purge-service-sa-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v1.39.8 # {"lp-deploy-tag-updater:version": "website-service"}
    spec:
      initContainers:
        - name: grab-config
        - name: render-config
      containers:
        - name: purge-service
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-production-website-long-life-v3#username}:${vault:database/creds/postgres-production-website-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}'
              valueFrom:
                $patch: delete
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-production-website-long-life-v3#username}:${vault:database/creds/postgres-production-website-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#READER_HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}'
            - name: VAULT_LOG_LEVEL
              value: ERROR
          resources:
            limits:
              cpu: '1'
              memory: '600Mi'
            requests:
              cpu: '300m'
              memory: '600Mi'
