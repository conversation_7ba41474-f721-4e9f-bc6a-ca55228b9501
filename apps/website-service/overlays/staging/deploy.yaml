apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-service-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v1.39.8 # {"lp-deploy-tag-updater:version": "website-service"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v1.39.8 # {"lp-deploy-tag-updater:version": "website-service"}
    spec:
      containers:
        - name: website-service
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-website-long-life-v4#username}:${vault:database/creds/postgres-staging-website-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}'
              valueFrom:
                $patch: delete
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-website-long-life-v4#username}:${vault:database/creds/postgres-staging-website-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#READER_HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}'
            - name: VAULT_LOG_LEVEL
              value: ERROR
          resources:
            limits:
              cpu: "1"
              memory: "550Mi"
            requests:
              cpu: "500m"
              memory: "350Mi"
