apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-etl-lookupapi-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.4.98 # {"$imagepolicy": "flux-system:mls-data-etl-lookupapi-flux-image-policy-production:tag"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-etl-lookupapi-sa-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.4.98 # {"$imagepolicy": "flux-system:mls-data-etl-lookupapi-flux-image-policy-production:tag"}
