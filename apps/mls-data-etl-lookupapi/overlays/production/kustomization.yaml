apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - path: deploy.yaml
  - path: hpa.yaml
  - path: sa.yaml
  - path: ingress-internal.yaml
  - path: ingress-public.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-etl-lookupapi
    newTag: v0.4.98 # {"$imagepolicy": "flux-system:mls-data-etl-lookupapi-flux-image-policy-production:tag"}
