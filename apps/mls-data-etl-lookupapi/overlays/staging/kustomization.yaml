apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - path: deploy.yaml
  - path: sa.yaml
  - path: hpa.yaml
  - path: ingress-internal.yaml
  - path: ingress-public.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-etl-lookupapi
    newTag: main-14673-45d94ff # {"$imagepolicy": "flux-system:mls-data-etl-lookupapi-flux-image-policy-staging-new:tag"}
