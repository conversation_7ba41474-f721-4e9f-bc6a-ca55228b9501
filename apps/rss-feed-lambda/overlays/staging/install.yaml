apiVersion: batch/v1
kind: Job
metadata:
  name: rss-feed-lambda-installer-
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/version: v4.3.2 # {"lp-deploy-tag-updater:version": "rss-feed-lambda"}
    spec:
      initContainers:
        - name: grab-config
          env:
            - name: CONFIG_TEMPLATE_PATH
              value: packages/rss-feed-lambda/src/config/config-template.yaml
