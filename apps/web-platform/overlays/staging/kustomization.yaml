apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - deploy-report.yaml
patches:
  - target:
      group: rbac.authorization.k8s.io
      kind: ClusterRole
      name: read-access-crossplane-web-platform
      version: v1
    patch: |-
      - op: replace
        path: /metadata/name
        value: read-access-crossplane-web-platform-staging
  - target:
      group: rbac.authorization.k8s.io
      kind: ClusterRoleBinding
      name: read-access-crossplane-web-platform-crb
      version: v1
    patch: |-
      - op: replace
        path: /subjects/0/namespace
        value: default
      - op: replace
        path: /metadata/name
        value: read-access-crossplane-web-platform-crb-staging
      - op: replace
        path: /roleRef/name
        value: read-access-crossplane-web-platform-staging
  - target:
      group: rbac.authorization.k8s.io
      kind: ClusterRole
      name: read-access-crossplane-web-platform
      version: v1
    patch: |-
      - op: replace
        path: /metadata/name
        value: read-access-crossplane-web-platform-staging
  - target:
      group: rbac.authorization.k8s.io
      kind: ClusterRoleBinding
      name: read-access-crossplane-web-platform-crb
      version: v1
    patch: |-
      - op: replace
        path: /subjects/0/namespace
        value: apps
      - op: replace
        path: /metadata/name
        value: read-access-crossplane-web-platform-crb-staging
      - op: replace
        path: /roleRef/name
        value: read-access-crossplane-web-platform-staging
  - path: sa.yaml
  - path: install.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/web-platform-artifacts
    newTag: v6.67.0 # {"lp-deploy-tag-updater:version": "web-platform"}
transformers:
  - version-suffixer.yaml
