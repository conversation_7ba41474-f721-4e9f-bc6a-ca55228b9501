apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - cron-job-import-liveby-data.yaml
  - cron-job-feed-region.yaml
  - cron-job-provider-filter.yaml
  - cron-job-group-listings.yaml
patches:
  - path: pdb.yaml
  - path: deploy.yaml
  - path: hpa.yaml
  - path: ingress.yaml
  - path: ingress-internal.yaml
  - path: sa.yaml
  - path: load-sql-migration-job.yaml
  - path: backfill-migration.yaml
  - path: kafka-consumer-deploy.yaml
images:
  - name: luxurypresence/load-search-service-migration
    newName: luxurypresence/load-search-service-migration
    newTag: v4.17.0 # {"lp-deploy-tag-updater:version": "search-service"}
  - name: luxurypresence/app
    newName: luxurypresence/search-service
    newTag: v4.17.0 # {"lp-deploy-tag-updater:version": "search-service"}
  - name: luxurypresence/region-data-loader
    newName: luxurypresence/region-data-loader
    newTag: v3.106.0 # {"lp-deploy-tag-updater:version": "region-data-loader"}
  - name: luxurypresence/search-feed-region-app
    newName: luxurypresence/search-feed-region-app
    newTag: v4.16.6 # {"lp-deploy-tag-updater:version": "feed-region-sync"}
