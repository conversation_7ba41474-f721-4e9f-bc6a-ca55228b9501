apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-service-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v4.17.0 # {"lp-deploy-tag-updater:version": "search-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: search-service-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v4.17.0 # {"lp-deploy-tag-updater:version": "search-service"}
    spec:
      containers:
        - name: search-service
          image: luxurypresence/app
          env:
            - name: DATABASE_HOST
              value: vault:secret/data/production/database-info/mls-v3#HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_READER_HOST
              value: vault:secret/data/production/database-info/mls-v3#READER_HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_PASSWORD
              value: vault:database/creds/postgres-production-search-long-life-v3#password
              valueFrom:
                $patch: delete
            - name: DATABASE_USERNAME
              value: vault:database/creds/postgres-production-search-long-life-v3#username
              valueFrom:
                $patch: delete
            - name: ENVIRONMENT
              value: production
          resources:
            limits:
              cpu: "1.5"
              memory: "2Gi"
            requests:
              cpu: "1.1"
              memory: "2Gi"
