apiVersion: batch/v1
kind: CronJob
metadata:
  name: search-service-cron-job-feed-region
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v4.16.6 # {"lp-deploy-tag-updater:version": "feed-region-sync"}
    tags.datadoghq.com/service: search-service-cron-job-feed-region
spec:
  schedule: "00 11 * * *"
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            vault.security.banzaicloud.io/vault-role: search-service-prod
            karpenter.sh/do-not-disrupt: "true"
          labels:
            tags.datadoghq.com/env: production
            tags.datadoghq.com/version: v4.16.6 # {"lp-deploy-tag-updater:version": "feed-region-sync"}
            luxurypresence/repository-name: search-service
            luxurypresence/package-name: feed-region-sync
        spec:
          serviceAccountName: search-service
          initContainers:
            - name: grab-config
              image: luxurypresence/search-feed-region-app
              env:
                - name: DEFAULT_CONFIG_TEMPLATE_PATHS
                  value: src/config/config-template.yaml,src/config-template.yaml,config-template.yaml,config/config-template.yaml
              command:
                - /bin/sh
                - -e
                - -c
                - |
                  # Initialize the not found paths variable
                  CONFIG_TEMPLATE_NOT_FOUND_PATHS=""

                  if [ -f "$CONFIG_TEMPLATE_PATH" ]; then
                    echo "Using template path specified with env: $CONFIG_TEMPLATE_PATH"
                    cp $CONFIG_TEMPLATE_PATH /template/config-template.yaml
                    exit 0
                  else
                    # Add the specific path to not found paths if it was set
                    if [ ! -z "$CONFIG_TEMPLATE_PATH" ]; then
                      CONFIG_TEMPLATE_NOT_FOUND_PATHS="$CONFIG_TEMPLATE_PATH"
                    fi
                  fi

                  I=1
                  while
                    CONFIG_TEMPLATE_PATH=$(echo "$DEFAULT_CONFIG_TEMPLATE_PATHS" | cut -d"," -f$I) || "";
                    if [ -f "$CONFIG_TEMPLATE_PATH" ]; then
                      echo "Discovered config in $CONFIG_TEMPLATE_PATH"
                      cp $CONFIG_TEMPLATE_PATH /template/config-template.yaml
                      break
                    else
                      # Add to not found paths if not empty
                      if [ ! -z "$CONFIG_TEMPLATE_PATH" ]; then
                        if [ -z "$CONFIG_TEMPLATE_NOT_FOUND_PATHS" ]; then
                          CONFIG_TEMPLATE_NOT_FOUND_PATHS="$CONFIG_TEMPLATE_PATH"
                        else
                          CONFIG_TEMPLATE_NOT_FOUND_PATHS="$CONFIG_TEMPLATE_NOT_FOUND_PATHS,$CONFIG_TEMPLATE_PATH"
                        fi
                      fi
                    fi
                    I=$((I+1))
                    [ ! -z "$CONFIG_TEMPLATE_PATH" ] || break;
                  do : ; done

                  # If no config template was found, create a fallback file
                  if [ ! -f "/template/config-template.yaml" ]; then
                    echo "No config template found in any of the searched paths"
                    echo "Creating fallback config-template.yaml with searched paths"
                    echo "SEARCHED_PATHS=$CONFIG_TEMPLATE_NOT_FOUND_PATHS" > /template/config-template.yaml
                  fi
              volumeMounts:
                - mountPath: /template
                  name: app-template
            - name: render-config
              image: luxurypresence/config-generator:3.8.0
              env:
                - name: ENVIRONMENT
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: APP_ENV
                - name: BASE_DOMAIN
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: BASE_DOMAIN
                - name: INTERNAL_BASE_DOMAIN
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: INTERNAL_BASE_DOMAIN
                - name: ACCOUNT_ID
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: ACCOUNT_ID
                - name: VAULT_ADDR
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: VAULT_ADDR
                - name: VAULT_ENV
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: VAULT_ENV
                - name: VAULT_PATH
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: VAULT_PATH
                - name: SA_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: spec.serviceAccountName
                - name: VAULT_ROLE # Take precedence over SA_NAME if specified
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.annotations['vault.security.banzaicloud.io/vault-role']
                - name: RELEASE_NOTIFICATION_URL
                  valueFrom:
                    configMapKeyRef:
                      name: cluster-info
                      key: RELEASE_NOTIFICATION_URL
                - name: REPOSITORY_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['luxurypresence/repository-name']
                - name: PACKAGE_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['luxurypresence/package-name']
                - name: VERSION
                  valueFrom:
                    fieldRef:
                      fieldPath: metadata.labels['tags.datadoghq.com/version']
              volumeMounts:
                - mountPath: /workspace/config-template.yaml
                  name: app-template
                  subPath: config-template.yaml
                - mountPath: /workspace/result
                  name: app-config
          containers:
            - name: search-service-cron-job-feed-region
              image: luxurypresence/search-feed-region-app
              env:
                - name: DATABASE_HOST
                  value: vault:secret/data/production/database-info/mls-v3#HOST
                - name: DATABASE_READER_HOST
                  value: vault:secret/data/production/database-info/mls-v3#READER_HOST
                - name: DATABASE_PASSWORD
                  value: vault:database/creds/postgres-production-search-long-life-v3#password
                - name: DATABASE_USERNAME
                  value: vault:database/creds/postgres-production-search-long-life-v3#username
                - name: READ_ONLY_REPLICA
                  value: vault:secret/data/production/database-info/mls-v3#READER_HOST_GLOW_RO
                - name: VAULT_LOG_LEVEL
                  value: ERROR
                - name: FEED_REGION_DRY_RUN
                  value: "false"
                - name: FEED_REGION_BATCH_SIZE
                  value: "500"
                - name: FEED_REGION_PIT_KEEP_ALIVE
                  value: "15m"
              resources:
                limits:
                  cpu: "1"
                  memory: "2Gi"
                requests:
                  cpu: "0.5"
                  memory: "2Gi"
              volumeMounts:
                # Dotenv projects
                - mountPath: /usr/src/app/.env
                  name: app-config
                  subPath: .env
                - mountPath: /usr/src/app/packages/search-service/.env
                  name: app-config
                  subPath: .env
                # Convict JavaScript projects
                - mountPath: /usr/src/app/src/config/config.json
                  name: app-config
                  subPath: config.json
                - mountPath: /usr/src/app/config.json
                  name: app-config
                  subPath: config.json
                - mountPath: /usr/src/app/packages/search-service/dist/config/config.json
                  name: app-config
                  subPath: config.json
                # Convict TypeScript projects
                - mountPath: /usr/src/app/dist/config/config.json
                  name: app-config
                  subPath: config.json
          volumes:
            - emptyDir: {}
              name: app-config
            - emptyDir: {}
              name: app-template
          imagePullSecrets:
            - name: image-pull-secret
          restartPolicy: Never
  concurrencyPolicy: Replace
