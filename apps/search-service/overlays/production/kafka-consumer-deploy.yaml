apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-service-kafka-consumer-deployment
  labels:
    run: search-service-kafka-consumer
    tags.datadoghq.com/service: search-service-kafka
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v4.17.0 # {"lp-deploy-tag-updater:version": "search-service"}
spec:
  replicas: 2
  selector:
    matchLabels:
      run: search-service-kafka-consumer
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: search-service-prod
      labels:
        tags.datadoghq.com/service: search-service-kafka
        run: search-service-kafka-consumer
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v4.17.0 # {"lp-deploy-tag-updater:version": "search-service"}
    spec:
      serviceAccountName: search-service
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: run
                      operator: In
                      values:
                        - search-service-kafka-consumer
                topologyKey: kubernetes.io/hostname
      containers:
        - name: search-service-kafka-consumer
          image: luxurypresence/app
          command: ["dumb-init"]
          args:
            [
              "sh",
              "-c",
              "STATEMENT_TIMEOUT=120s node dist/kafka-consumer/src/main.js",
            ]
          env:
            - name: DATABASE_NAME
              value: "search_service"
            - name: DATABASE_PORT
              value: "5432"
            - name: DATABASE_HOST
              value: vault:secret/data/production/database-info/mls-v3#HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_READER_HOST
              value: vault:secret/data/production/database-info/mls-v3#READER_HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_PASSWORD
              value: vault:database/creds/postgres-production-search-long-life-v3#password
              valueFrom:
                $patch: delete
            - name: DATABASE_USERNAME
              value: vault:database/creds/postgres-production-search-long-life-v3#username
              valueFrom:
                $patch: delete
            - name: VAULT_LOG_LEVEL
              value: ERROR
            - name: ENVIRONMENT
              value: production
          resources:
            limits:
              cpu: "1"
              memory: "2Gi"
            requests:
              cpu: "0.5"
              memory: "2Gi"
          ports:
            - containerPort: 8010
          # the readiness probe details
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - "pgrep -f 'dist/kafka-consumer/src/main.js' || exit 1"
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 2 # Increase to 3 if the process is unstable
            timeoutSeconds: 3
          volumeMounts:
            - mountPath: /usr/src/app/packages/search-service/dist/config/config.json
              name: app-config
              subPath: config.json
            - mountPath: /usr/src/app/packages/search-service/.env
              name: app-config
              subPath: .env
      imagePullSecrets:
        - name: image-pull-secret
