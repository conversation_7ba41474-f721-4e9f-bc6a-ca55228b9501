apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-service-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v4.18.0 # {"lp-deploy-tag-updater:version": "search-service"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v4.18.0 # {"lp-deploy-tag-updater:version": "search-service"}
    spec:
      containers:
        - name: search-service
          image: luxurypresence/app
          env:
            - name: DATABASE_HOST
              value: vault:secret/data/staging/database-info/mls-v4#HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_READER_HOST
              value: vault:secret/data/staging/database-info/mls-v4#READER_HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_PASSWORD
              value: vault:database/creds/postgres-staging-search-long-life-v4#password
              valueFrom:
                $patch: delete
            - name: DATABASE_USERNAME
              value: vault:database/creds/postgres-staging-search-long-life-v4#username
              valueFrom:
                $patch: delete
            - name: VAULT_LOG_LEVEL
              value: ERROR
            - name: ENVIRONMENT
              value: staging
          resources:
            limits:
              cpu: "1"
              memory: "1Gi"
            requests:
              cpu: "0.5"
              memory: "1Gi"
