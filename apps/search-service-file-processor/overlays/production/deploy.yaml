apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-service-file-processor-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v4.16.6 # {"lp-deploy-tag-updater:version": "file-processor"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: search-service-file-processor
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v4.16.6 # {"lp-deploy-tag-updater:version": "file-processor"}
    spec:
      containers:
        - name: search-service-file-processor
          image: luxurypresence/app
          env:
            - name: DATABASE_HOST
              value: vault:secret/data/production/database-info/mls-v3#HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_READER_HOST
              value: vault:secret/data/production/database-info/mls-v3#READER_HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_PASSWORD
              value: vault:database/creds/postgres-production-search-long-life-v3#password
              valueFrom:
                $patch: delete
            - name: DATABASE_USERNAME
              value: vault:database/creds/postgres-production-search-long-life-v3#username
              valueFrom:
                $patch: delete
            - name: NODE_OPTIONS
              value: "--max-old-space-size=8192"
          resources:
            limits:
              cpu: "1.15"
              memory: "10Gi"
            requests:
              cpu: "1.15"
              memory: "10Gi"
