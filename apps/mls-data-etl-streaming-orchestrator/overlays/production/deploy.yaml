apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-etl-streaming-orchestrator-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.4.98 # {"$imagepolicy": "flux-system:mls-data-etl-streaming-orchestrator-flux-image-policy-production:tag"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-etl-streaming-orchestrator-sa-prod
        karpenter.sh/do-not-disrupt: "true"
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.4.98 # {"$imagepolicy": "flux-system:mls-data-etl-streaming-orchestrator-flux-image-policy-production:tag"}
