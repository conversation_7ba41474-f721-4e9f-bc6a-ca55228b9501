apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
  - mls-data-etl-streaming-orchestrator-dd-metric.yaml
patches:
  - path: deploy.yaml
  - path: sa.yaml
  - path: hpa.yaml
  - path: ingress-internal.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-etl-streaming-orchestrator
    newTag: main-14694-364873f # {"$imagepolicy": "flux-system:mls-data-etl-streaming-orchestrator-flux-image-policy-staging-new:tag"}
