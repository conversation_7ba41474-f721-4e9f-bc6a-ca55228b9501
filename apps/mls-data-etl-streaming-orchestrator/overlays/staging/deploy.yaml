apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-etl-streaming-orchestrator-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: main-14694-364873f # {"$imagepolicy": "flux-system:mls-data-etl-streaming-orchestrator-flux-image-policy-staging-new:tag"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-etl-streaming-orchestrator-sa
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: main-14694-364873f # {"$imagepolicy": "flux-system:mls-data-etl-streaming-orchestrator-flux-image-policy-staging-new:tag"}
