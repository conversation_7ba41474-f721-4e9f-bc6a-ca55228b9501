apiVersion: batch/v1
kind: Job
metadata:
  name: anywhere-lambda-installer-
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/version: v0.0.14 # {"lp-deploy-tag-updater:version": "anywhere"}
    spec:
      containers:
        - name: app
          env:
            - name: DB_NAME
              value: vault:secret/data/staging/database-info/main-v4#DB
            - name: DB_HOST
              value: vault:secret/data/staging/database-info/main-v4#HOST
            - name: DB_READER_HOST
              value: vault:secret/data/staging/database-info/main-v4#READER_HOST
