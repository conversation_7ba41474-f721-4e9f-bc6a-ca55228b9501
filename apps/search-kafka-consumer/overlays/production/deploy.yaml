apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-kafka-consumer-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v1.9.6 # {"lp-deploy-tag-updater:version": "kafka-consumer"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: search-kafka-consumer-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v1.9.6 # {"lp-deploy-tag-updater:version": "kafka-consumer"}
    spec:
      containers:
        - name: search-kafka-consumer
          image: luxurypresence/app
          env:
            - name: DATABASE_HOST
              value: vault:secret/data/production/database-info/mls-v3#HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_READER_HOST
              value: vault:secret/data/production/database-info/mls-v3#READER_HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_PASSWORD
              value: vault:database/creds/postgres-production-search-long-life-v3#password
              valueFrom:
                $patch: delete
            - name: DATABASE_USERNAME
              value: vault:database/creds/postgres-production-search-long-life-v3#username
              valueFrom:
                $patch: delete
            - name: ENVIRONMENT
              value: production
          resources:
            limits:
              cpu: 1
              memory: '500Mi'
            requests:
              cpu: '300m'
              memory: '500Mi'
