apiVersion: apps/v1
kind: Deployment
metadata:
  name: search-kafka-consumer-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v1.9.7 # {"lp-deploy-tag-updater:version": "kafka-consumer"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v1.9.7 # {"lp-deploy-tag-updater:version": "kafka-consumer"}
    spec:
      containers:
        - name: search-kafka-consumer
          image: luxurypresence/app
          env:
            - name: DATABASE_HOST
              value: vault:secret/data/staging/database-info/mls-v4#HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_READER_HOST
              value: vault:secret/data/staging/database-info/mls-v4#READER_HOST
              valueFrom:
                $patch: delete
            - name: DATABASE_PASSWORD
              value: vault:database/creds/postgres-staging-search-long-life-v4#password
              valueFrom:
                $patch: delete
            - name: DATABASE_USERNAME
              value: vault:database/creds/postgres-staging-search-long-life-v4#username
              valueFrom:
                $patch: delete
            - name: VAULT_LOG_LEVEL
              value: ERROR
            - name: ENVIRONMENT
              value: staging
