apiVersion: batch/v1
kind: Job
metadata:
  name: slide-micro-installer-
spec:
  template:
    spec:
      containers:
        - name: deploy
          env:
            - name: VERSION
              value: v3.35.1 # {"lp-deploy-tag-updater:version": "slide-scene-micro"}
            - name: USERNAME
              value: vault:secret/data/staging/import-map-deployer#USERNAME
            - name: PASSWORD
              value: vault:secret/data/staging/import-map-deployer#PASSWORD
            - name: MICRO_CDN
              value: dsz7if8ketii4.cloudfront.net
          args:
            - >-
              echo "Installing..."; aws s3 cp --acl public-read --recursive --cache-control "public" /artefacts s3://$BUCKET/${MICRO_SERVICE_NAME}/$VERSION; aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DIST_ID --paths "/*"; curl -u "${USERNAME}:${PASSWORD}" -X PATCH $IMPORT_MAP_DEPLOYER_URL/services\?env=nsDefault --data "{\"service\": \"@luxurypresence/${MICRO_SERVICE_NAME}\", \"url\": \"https://${MICRO_CDN}/${MICRO_SERVICE_NAME}/${VERSION}/app.min.js\"}" -H "Accept: application/json" -H "Content-Type: application/json"; curl -u "${USERNAME}:${PASSWORD}" -X PATCH $IMPORT_MAP_DEPLOYER_URL/services\?env=nsDefault --data "{\"service\": \"@luxurypresence/${MICRO_SERVICE_NAME}-config\", \"url\": \"https://${MICRO_CDN}/${MICRO_SERVICE_NAME}/${VERSION}/config.js\"}" -H "Accept: application/json" -H "Content-Type: application/json";
