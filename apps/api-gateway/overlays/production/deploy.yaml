apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v5.169.15 # {"lp-deploy-tag-updater:version": "api-gateway"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: api-gateway-sa-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v5.169.15 # {"lp-deploy-tag-updater:version": "api-gateway"}
    spec:
      initContainers:
        - name: grab-config
        - name: render-config
          env:
            - name: AUTH0_HOOK_API_KEYS
              $patch: delete
            - name: AUTH0_DOMAIN
              $patch: delete
            - name: AUTH0_CLIENT_ID
              $patch: delete
            - name: AUTH0_CLIENT_SECRET
              $patch: delete
            - name: WEB_PLATFORM_CLIENT_ID
              $patch: delete
            - name: VAULT_LOG_LEVEL
              value: ERROR
      containers:
        - name: api-gateway
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-production-core-long-life-v3#username}:${vault:database/creds/postgres-production-core-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}'
              valueFrom:
                $patch: delete
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-production-core-long-life-v3#username}:${vault:database/creds/postgres-production-core-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#READER_HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}'
            - name: VAULT_LOG_LEVEL
              value: ERROR
          resources:
            limits:
              cpu: "1.1"
              memory: "1300Mi"
            requests:
              cpu: "1"
              memory: "1300Mi"
