apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway-deployment
  labels:
    tags.datadoghq.com/env: staging
    tags.datadoghq.com/version: v5.169.18 # {"lp-deploy-tag-updater:version": "api-gateway"}
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: staging
        tags.datadoghq.com/version: v5.169.18 # {"lp-deploy-tag-updater:version": "api-gateway"}
    spec:
      initContainers:
        - name: grab-config
        - name: render-config
          env:
            - name: AUTH0_HOOK_API_KEYS
              $patch: delete
            - name: AUTH0_DOMAIN
              $patch: delete
            - name: AUTH0_CLIENT_ID
              $patch: delete
            - name: AUTH0_CLIENT_SECRET
              $patch: delete
            - name: WEB_PLATFORM_CLIENT_ID
              $patch: delete
            - name: VAULT_LOG_LEVEL
              value: ERROR
      containers:
        - name: api-gateway
          env:
            - name: PG_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-core-long-life-v4#username}:${vault:database/creds/postgres-staging-core-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}'
              valueFrom:
                $patch: delete
            - name: PG_READER_CONNECTION_STRING
              value: 'postgresql://${vault:database/creds/postgres-staging-core-long-life-v4#username}:${vault:database/creds/postgres-staging-core-long-life-v4#password}@${vault:secret/data/staging/database-info/main-v4#READER_HOST}:5432/${vault:secret/data/staging/database-info/main-v4#DB}'
            - name: VAULT_DISASTER_RECOVERY_PG_CONNECTION_STRING
              valueFrom:
                $patch: delete
            - name: VAULT_LOG_LEVEL
              value: ERROR
            - name: DD_LOGS_INJECTION
              value: "true"
          resources:
            limits:
              cpu: "1"
              memory: "750Mi"
            requests:
              cpu: "500m"
              memory: "750Mi"
