apiVersion: batch/v1
kind: Job
metadata:
  name: mls-data-etl-transform-job
spec:
  template:
    spec:
      containers:
        - name: aws-cli
          env:
            - name: TRANSFORM_BUCKET
              value: "lp-datalakehouse-stage"
            - name: ENV
              value: "staging"
            - name: VERSION
              value: main-14682-3527f6d # {"$imagepolicy": "flux-system:mls-data-etl-transform-flux-image-policy-staging-new:tag"}
            - name: ORCHESTRATOR_URL
              value: https://mls-data-etl-streaming-orchestrator.luxurycoders.com
