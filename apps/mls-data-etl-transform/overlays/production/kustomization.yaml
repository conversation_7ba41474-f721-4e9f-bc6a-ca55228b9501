apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - target:
      group: batch
      kind: Job
      name: mls-data-etl-transform-job
      version: v1
    patch: |-
      - op: replace
        path: /metadata/name
        value: mls-data-etl-transform-job-production-
  - path: job.yaml
  - path: sa.yaml
images:
  - name: luxurypresence/data-etl-transform
    newTag: v0.4.98 # {"$imagepolicy": "flux-system:mls-data-etl-transform-flux-image-policy-production:tag"}
transformers:
  - version-suffixer.yaml
