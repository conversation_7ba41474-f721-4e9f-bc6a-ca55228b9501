apiVersion: batch/v1
kind: Job
metadata:
  name: mls-data-etl-transform-job
spec:
  template:
    spec:
      containers:
        - name: aws-cli
          env:
            - name: TRANSFORM_BUCKET
              value: "lp-datalakehouse-production"
            - name: ENV
              value: "production"
            - name: VERSION
              value: v0.4.98 # {"$imagepolicy": "flux-system:mls-data-etl-transform-flux-image-policy-production:tag"}
            - name: ORCHESTRATOR_URL
              value: https://mls-data-etl-streaming-orchestrator.luxurypresence.com
          resources:
            limits:
              cpu: "2"
              memory: 1Gi
            requests:
              cpu: "1"
              memory: 500Mi
