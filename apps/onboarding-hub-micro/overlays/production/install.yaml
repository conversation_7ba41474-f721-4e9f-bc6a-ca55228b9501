apiVersion: batch/v1
kind: Job
metadata:
  name: onboarding-hub-micro-installer-
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/version: v0.18.9 # {"lp-deploy-tag-updater:version": "onboarding-hub-micro"}
    spec:
      containers:
        - name: deploy
          env:
            - name: VERSION
              value: v0.18.9 # {"lp-deploy-tag-updater:version": "onboarding-hub-micro"}
            - name: IMPORT_MAP_DEPLOYER_ENV
              value: nsProduction
            - name: IMPORT_MAP_DEPLOYER_USERNAME
              value: vault:secret/data/production/import-map-deployer#USERNAME
            - name: IMPORT_MAP_DEPLOYER_PASSWORD
              value: vault:secret/data/production/import-map-deployer#PASSWORD
            - name: UPDATE_IMPORT_MAP
              value: "true"
