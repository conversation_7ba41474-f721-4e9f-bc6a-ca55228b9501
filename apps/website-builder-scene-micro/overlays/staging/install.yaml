apiVersion: batch/v1
kind: Job
metadata:
  name: website-builder-scene-micro-installer-
spec:
  template:
    metadata:
      labels:
        tags.datadoghq.com/version: v0.63.1 # {"lp-deploy-tag-updater:version": "website-builder-scene-micro"}
    spec:
      containers:
        - name: deploy
          env:
            - name: VERSION
              value: v0.63.1 # {"lp-deploy-tag-updater:version": "website-builder-scene-micro"}
            - name: IMPORT_MAP_DEPLOYER_ENV
              value: nsDefault
            - name: IMPORT_MAP_DEPLOYER_USERNAME
              value: vault:secret/data/staging/import-map-deployer#USERNAME
            - name: IMPORT_MAP_DEPLOYER_PASSWORD
              value: vault:secret/data/staging/import-map-deployer#PASSWORD
            - name: UPDATE_IMPORT_MAP
              value: "true"
