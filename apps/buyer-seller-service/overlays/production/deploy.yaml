apiVersion: apps/v1
kind: Deployment
metadata:
  name: buyer-seller-service-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v2.24.0 # {"lp-deploy-tag-updater:version": "buyer-seller-service"}
spec:
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: buyer-seller-service-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v2.24.0 # {"lp-deploy-tag-updater:version": "buyer-seller-service"}
    spec:
      containers:
        - name: buyer-seller-service
          image: luxurypresence/app
          env:
            - name: PG_CONNECTION_STRING
              value: "postgresql://${vault:database/creds/postgres-production-buyerseller-long-life-v3#username}:${vault:database/creds/postgres-production-buyerseller-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}?schema=buyerseller"
            - name: PG_READER_CONNECTION_STRING
              value: "postgresql://${vault:database/creds/postgres-production-buyerseller-long-life-v3#username}:${vault:database/creds/postgres-production-buyerseller-long-life-v3#password}@${vault:secret/data/production/database-info/main-v3#READER_HOST}:5432/${vault:secret/data/production/database-info/main-v3#DB}?schema=buyerseller"
            - name: GOOGLE_CLIENT_ID
              value: vault:secret/data/production/buyer-seller#GOOGLE_CLIENT_ID
            - name: FACEBOOK_APP_ID
              value: vault:secret/data/production/buyer-seller#FACEBOOK_APP_ID
            - name: JWT_SECRET
              value: vault:secret/data/production/buyer-seller#JWT_SECRET
            - name: VAULT_DISASTER_RECOVERY_PG_CONNECTION_STRING
              valueFrom:
                $patch: delete
            - name: LAUNCHDARKLY_KEY
              value: vault:secret/data/production/standard#LAUNCHDARKLY_KEY
            - name: SEARCH_SERVICE_USER
              value: vault:secret/data/production/buyer-seller#SEARCH_SERVICE_USER
            - name: SEARCH_SERVICE_PASSWORD
              value: vault:secret/data/production/buyer-seller#SEARCH_SERVICE_PASSWORD
            - name: VAULT_LOG_LEVEL
              value: ERROR
          resources:
            limits:
              cpu: "1150m"
              memory: "3Gi"
            requests:
              cpu: "500m"
              memory: "3Gi"
