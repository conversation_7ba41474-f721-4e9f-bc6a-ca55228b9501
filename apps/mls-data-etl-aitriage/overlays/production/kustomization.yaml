apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - path: deploy.yaml
  - path: hpa.yaml
  - path: sa.yaml
  - path: ingress-internal.yaml
  - path: ingress-public.yaml
images:
  - name: luxurypresence/app
    newName: luxurypresence/mls-data-etl-aitriage
    newTag: v0.5.1 # {"$imagepolicy": "flux-system:mls-data-etl-aitriage-flux-image-policy-production:tag"}
