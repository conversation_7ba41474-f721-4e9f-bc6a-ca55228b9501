apiVersion: apps/v1
kind: Deployment
metadata:
  name: mls-data-etl-aitriage-deployment
  labels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/version: v0.5.1 # {"$imagepolicy": "flux-system:mls-data-etl-aitriage-flux-image-policy-production:tag"}
spec:
  replicas: 0
  template:
    metadata:
      annotations:
        vault.security.banzaicloud.io/vault-role: mls-data-etl-aitriage-sa-prod
      labels:
        tags.datadoghq.com/env: production
        tags.datadoghq.com/version: v0.5.1 # {"$imagepolicy": "flux-system:mls-data-etl-aitriage-flux-image-policy-production:tag"}
